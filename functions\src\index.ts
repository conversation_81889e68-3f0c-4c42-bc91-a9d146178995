// Minimal functions index - only essential webhook functionality
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';
import { createHash } from 'crypto';
import cors from 'cors';
import Stripe from 'stripe';

// Initialize Firebase Admin
admin.initializeApp();

// Initialize Stripe
const stripe = new Stripe(functions.config().stripe?.api_key || process.env.STRIPE_API_KEY || '', {
  apiVersion: '2025-05-28.basil',
});

// CORS configuration for development and production
const corsHandler = cors({
  origin: [
    'https://h1c1-798a8.web.app',
    'https://h1c1-798a8.firebaseapp.com',
    'https://hivecampus.app',
    'https://www.hivecampus.app',
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:3000',
    'http://localhost:5000'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});

// Import simple admin notification functions
// export * from './simple-admin-notifications'; // Temporarily disabled to fix deployment timeout

// Import notification functions
// export * from './notifications'; // Temporarily disabled to fix deployment timeout

// Import broadcast notification functions
// export * from './broadcast-notifications'; // Temporarily disabled due to deployment issues

console.log('🚀 Firebase Functions loading...');

// Helper function to generate 6-digit secret code
function generateSecretCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Essential Stripe webhook - only handles payment completion
export const essentialWebhook = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onRequest(async (req, res) => {
    try {
      console.log('🔗 Essential webhook received');

      if (req.method !== 'POST') {
        res.status(405).send('Method not allowed');
        return;
      }

      const event = req.body;
      console.log(`📨 Event type: ${event.type}`);

      // Handle checkout session completed
      if (event.type === 'checkout.session.completed') {
        const session = event.data.object;
        const metadata = session.metadata;

        if (metadata?.orderId) {
          const orderId = metadata.orderId;
          console.log(`📦 Processing order: ${orderId}`);

          // Get order
          const orderRef = admin.firestore().collection('orders').doc(orderId);
          const orderDoc = await orderRef.get();

          if (orderDoc.exists) {
            const orderData = orderDoc.data();
            
            // Generate secret code
            const secretCode = generateSecretCode();
            console.log(`🔐 Generated code: ${secretCode}`);

            // Update order
            await orderRef.update({
              status: 'payment_succeeded',
              secretCode: secretCode,
              paymentCompletedAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });

            // Update listing to sold
            if (orderData?.listingId) {
              await admin.firestore().collection('listings').doc(orderData.listingId).update({
                status: 'sold',
                soldAt: admin.firestore.Timestamp.now(),
                updatedAt: admin.firestore.Timestamp.now()
              });
              console.log(`✅ Listing ${orderData.listingId} marked as sold`);
            }

            // Send buyer notification using new notification system
            if (orderData?.buyerId) {
              try {
                // Create in-app notification directly (since we're in the backend)
                await admin.firestore().collection(`users/${orderData.buyerId}/notifications`).add({
                  type: 'payment_success',
                  title: 'Payment Successful!',
                  message: `Your payment has been processed. Secret code: ${secretCode}`,
                  icon: '/icons/icon-192.png',
                  createdAt: admin.firestore.Timestamp.now(),
                  read: false,
                  link: `/orders/${orderId}`,
                  priority: 'high',
                  actionRequired: false,
                  metadata: {},
                  orderId: orderId,
                  secretCode: secretCode,
                  amount: orderData.totalAmount
                });

                // Also send push notification if user has FCM token
                const tokenDoc = await admin.firestore()
                  .doc(`users/${orderData.buyerId}/fcmTokens/web`)
                  .get();

                if (tokenDoc.exists && tokenDoc.data()?.active && tokenDoc.data()?.token) {
                  const payload: admin.messaging.Message = {
                    token: tokenDoc.data()!.token,
                    notification: {
                      title: 'Payment Successful!',
                      body: `Your payment has been processed. Secret code: ${secretCode}`,
                      imageUrl: '/icons/icon-192.png'
                    },
                    data: {
                      type: 'payment_success',
                      link: `/orders/${orderId}`,
                      orderId: orderId,
                      requireInteraction: 'true'
                    },
                    webpush: {
                      headers: {
                        TTL: '86400',
                        Urgency: 'high'
                      },
                      notification: {
                        icon: '/icons/icon-192.png',
                        badge: '/icons/icon-96.png',
                        tag: 'payment_success',
                        requireInteraction: true,
                        actions: [
                          { action: 'view', title: 'View Order' },
                          { action: 'dismiss', title: 'Dismiss' }
                        ],
                        vibrate: [200, 100, 200]
                      }
                    }
                  };

                  try {
                    await admin.messaging().send(payload);
                    console.log(`Push notification sent for payment success to user ${orderData.buyerId}`);
                  } catch (pushError) {
                    console.error('Error sending push notification:', pushError);
                    // Mark token as inactive if it's invalid
                    if ((pushError as any).code === 'messaging/registration-token-not-registered') {
                      await admin.firestore()
                        .doc(`users/${orderData.buyerId}/fcmTokens/web`)
                        .update({ active: false });
                    }
                  }
                }
              } catch (notificationError) {
                console.error('Error sending payment success notification:', notificationError);
                // Don't fail the webhook if notification fails
              }
            }

            // Create admin notification for payment completion - temporarily disabled
            // try {
            //   if (orderData) {
            //     // Get user data for better notification
            //     const buyerDoc = await admin.firestore().collection('users').doc(orderData.buyerId).get();
            //     const buyerData = buyerDoc.exists ? buyerDoc.data() : null;
            //     const buyerName = buyerData?.name || buyerData?.displayName || buyerData?.email?.split('@')[0] || 'User';

            //     await createAdminNotification(
            //       'payment_completed',
            //       'Payment Completed',
            //       `${buyerName} completed payment of $${orderData.totalAmount}`,
            //       {
            //         userId: orderData.buyerId,
            //         username: buyerName,
            //         orderId: orderId,
            //         amount: orderData.totalAmount,
            //         metadata: {
            //           secretCode: secretCode,
            //           listingId: orderData.listingId,
            //           sellerId: orderData.sellerId
            //         },
            //         actionUrl: `/admin/transactions?search=${orderId}`
            //       }
            //     );
            //   }
            // } catch (notificationError) {
            //   console.error('Error creating admin notification:', notificationError);
            //   // Don't fail the webhook if notification fails
            // }

            console.log(`✅ Order ${orderId} processed successfully`);
          }
        }
      }

      // Handle payment failures
      if (event.type === 'payment_intent.payment_failed') {
        const paymentIntent = event.data.object;
        const metadata = paymentIntent.metadata;

        if (metadata?.orderId) {
          const orderId = metadata.orderId;
          console.log(`❌ Payment failed for order: ${orderId}`);

          try {
            // Get order
            const orderRef = admin.firestore().collection('orders').doc(orderId);
            const orderDoc = await orderRef.get();

            if (orderDoc.exists) {
              const orderData = orderDoc.data();

              if (orderData) {
                // Update order status
                await orderRef.update({
                  status: 'payment_failed',
                  paymentFailedAt: admin.firestore.Timestamp.now(),
                  updatedAt: admin.firestore.Timestamp.now()
                });

                // Get user data for notification
                const buyerDoc = await admin.firestore().collection('users').doc(orderData.buyerId).get();
                const buyerData = buyerDoc.exists ? buyerDoc.data() : null;
                const buyerName = buyerData?.name || buyerData?.displayName || buyerData?.email?.split('@')[0] || 'User';

                // Create admin notification for payment failure - temporarily disabled
                // await createAdminNotification(
                //   'payment_failed',
                //   'Payment Failed',
                //   `Payment failed for ${buyerName}'s order of $${orderData.totalAmount}`,
                //   {
                //     userId: orderData.buyerId,
                //     username: buyerName,
                //     orderId: orderId,
                //     amount: orderData.totalAmount,
                //     metadata: {
                //       failureReason: paymentIntent.last_payment_error?.message || 'Unknown error',
                //       listingId: orderData.listingId,
                //       sellerId: orderData.sellerId
                //     },
                //     actionUrl: `/admin/transactions?search=${orderId}`
                //   }
                // );

                console.log(`❌ Payment failure processed for order: ${orderId}`);
              }
            }
          } catch (error) {
            console.error('Error processing payment failure:', error);
          }
        }
      }

      res.status(200).json({ received: true });

    } catch (error) {
      console.error('❌ Webhook error:', error);
      res.status(500).send('Webhook failed');
    }
  });

// Test function
export const testEssential = functions
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Essential webhook working',
      testCode: generateSecretCode(),
      timestamp: new Date().toISOString()
    });
  });

// Stripe API Express app for checkout sessions
export const stripeApi = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60,
  })
  .https.onRequest(async (req, res) => {
    // Enable CORS
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      res.status(200).send();
      return;
    }

    try {
      const path = req.path;

      if (path === '/create-checkout-session' && req.method === 'POST') {
        await handleCreateCheckoutSession(req, res);
      } else {
        res.status(404).json({ error: 'Endpoint not found' });
      }
    } catch (error) {
      console.error('Error in stripeApi:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  });

// Handle checkout session creation
async function handleCreateCheckoutSession(req: any, res: any) {
  try {
    // Verify authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Unauthorized - No valid authorization header' });
      return;
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(token);
    const buyerId = decodedToken.uid;

    // Get request data
    const { listingId, quantity = 1, useWalletBalance = false, orderDetails } = req.body;
    const walletBalanceUsed = orderDetails?.appliedWalletCredit || 0;

    if (!listingId) {
      res.status(400).json({ error: 'Listing ID is required' });
      return;
    }

    console.log(`🛒 Creating checkout for listing: ${listingId}, wallet: $${walletBalanceUsed}`);

    // Get listing details
    const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
    if (!listingDoc.exists) {
      res.status(404).json({ error: 'Listing not found' });
      return;
    }

    const listing = listingDoc.data();
    if (!listing) {
      res.status(404).json({ error: 'Listing data not found' });
      return;
    }

    // Validate listing is available
    if (listing.status !== 'active') {
      res.status(400).json({ error: 'Listing is not available for purchase' });
      return;
    }

    // Prevent self-purchase
    if (listing.sellerId === buyerId) {
      res.status(400).json({ error: 'You cannot purchase your own listing' });
      return;
    }

    // Calculate pricing
    const basePrice = orderDetails?.price || listing.price;
    const shippingFee = orderDetails?.shippingFee || 0;
    const totalBeforeWallet = basePrice + shippingFee;

    // Apply wallet balance
    const actualWalletUsed = Math.min(walletBalanceUsed, totalBeforeWallet);
    const finalAmount = Math.max(0, totalBeforeWallet - actualWalletUsed);

    // Handle zero-amount orders (fully paid with wallet)
    if (finalAmount === 0) {
      // Create order directly without Stripe
      const orderId = admin.firestore().collection('orders').doc().id;
      const orderData = {
        id: orderId,
        listingId,
        buyerId,
        sellerId: listing.sellerId,
        status: 'payment_succeeded',
        totalAmount: totalBeforeWallet,
        walletAmountUsed: actualWalletUsed,
        stripeAmount: 0,
        paymentMethod: 'wallet',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        listingTitle: listing.title,
        listingPrice: basePrice,
        shippingFee,
        orderDetails
      };

      await admin.firestore().collection('orders').doc(orderId).set(orderData);

      // Deduct wallet balance
      if (actualWalletUsed > 0) {
        const walletRef = admin.firestore().collection('wallets').doc(buyerId);
        await admin.firestore().runTransaction(async (transaction) => {
          const walletDoc = await transaction.get(walletRef);
          const currentBalance = walletDoc.exists ? walletDoc.data()?.balance || 0 : 0;

          if (currentBalance < actualWalletUsed) {
            throw new Error('Insufficient wallet balance');
          }

          transaction.update(walletRef, {
            balance: currentBalance - actualWalletUsed,
            lastUpdated: admin.firestore.Timestamp.now()
          });
        });
      }

      res.status(200).json({
        success: true,
        walletPayment: true,
        orderId,
        walletAmountUsed: actualWalletUsed,
        message: 'Order completed with wallet balance'
      });
      return;
    }

    // Create Stripe checkout session for remaining amount
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [{
        price_data: {
          currency: 'usd',
          product_data: {
            name: `${listing.title}${actualWalletUsed > 0 ? ` (after $${actualWalletUsed} wallet credit)` : ''}`,
            description: listing.description || 'Hive Campus item',
          },
          unit_amount: Math.round(finalAmount * 100),
        },
        quantity: 1,
      }],
      mode: 'payment',
      success_url: `https://h1c1-798a8.web.app/order-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `https://h1c1-798a8.web.app/listing/${listingId}`,
      metadata: {
        listingId,
        buyerId,
        sellerId: listing.sellerId,
        walletAmountUsed: actualWalletUsed.toString(),
        originalTotal: totalBeforeWallet.toString(),
        finalAmount: finalAmount.toString(),
      },
    });

    res.status(200).json({
      success: true,
      sessionId: session.id,
      sessionUrl: session.url,
      walletAmountUsed: actualWalletUsed,
      finalStripeAmount: finalAmount,
      originalTotal: totalBeforeWallet
    });

    console.log(`✅ Checkout session created: ${session.id} for $${finalAmount}`);

  } catch (error) {
    console.error('❌ Error creating checkout session:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ error: errorMessage });
  }
}

// Release funds with secret code (alias for compatibility)
export const releaseEscrowWithCode = functions
  .https.onCall(async (data, context) => {
    try {
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
      }

      const { orderId, secretCode } = data;
      
      if (!orderId || !secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code required');
      }

      // Get order
      const orderRef = admin.firestore().collection('orders').doc(orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      // Verify buyer
      if (orderData?.buyerId !== context.auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'Not authorized');
      }

      // Verify secret code
      if (orderData?.secretCode !== secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
      }

      // Update order status
      await orderRef.update({
        status: 'completed',
        fundsReleased: true,
        fundsReleasedAt: admin.firestore.Timestamp.now(),
        completedAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });

      // Update listing to sold (if not already sold)
      if (orderData?.listingId) {
        const listingRef = admin.firestore().collection('listings').doc(orderData.listingId);
        const listingDoc = await listingRef.get();

        if (listingDoc.exists && listingDoc.data()?.status !== 'sold') {
          await listingRef.update({
            status: 'sold',
            soldAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
          });
          console.log(`✅ Listing ${orderData.listingId} marked as sold via secret code`);
        }
      }

      console.log(`✅ Funds released for order: ${orderId}`);

      return {
        success: true,
        message: 'Funds released successfully'
      };

    } catch (error) {
      console.error('Error releasing funds:', error);
      throw error;
    }
  });

// Release funds with secret code (new name)
export const releaseFundsWithCode = functions
  .https.onCall(async (data, context) => {
    try {
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
      }

      const { orderId, secretCode } = data;
      
      if (!orderId || !secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code required');
      }

      // Get order
      const orderRef = admin.firestore().collection('orders').doc(orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      // Verify buyer
      if (orderData?.buyerId !== context.auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'Not authorized');
      }

      // Verify secret code
      if (orderData?.secretCode !== secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
      }

      // Update order status
      await orderRef.update({
        status: 'completed',
        fundsReleased: true,
        fundsReleasedAt: admin.firestore.Timestamp.now(),
        completedAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });

      // Update listing to sold (if not already sold)
      if (orderData?.listingId) {
        const listingRef = admin.firestore().collection('listings').doc(orderData.listingId);
        const listingDoc = await listingRef.get();

        if (listingDoc.exists && listingDoc.data()?.status !== 'sold') {
          await listingRef.update({
            status: 'sold',
            soldAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
          });
          console.log(`✅ Listing ${orderData.listingId} marked as sold via secret code`);
        }
      }

      console.log(`✅ Funds released for order: ${orderId}`);

      return {
        success: true,
        message: 'Funds released successfully'
      };

    } catch (error) {
      console.error('Error releasing funds:', error);
      throw error;
    }
  });


// Function to set admin PIN
export const setAdminPin = functions.https.onCall(async (data, context) => {
  try {
    // Verify the user is authenticated and is an admin
    if (!context.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'User must be authenticated'
      );
    }

    const { pin } = data;

    if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'PIN must be exactly 8 digits'
      );
    }

    // Verify user is admin
    const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
    if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admin users can set PIN'
      );
    }

    // Hash the PIN for security
    const hashedPin = createHash('sha256').update(pin).digest('hex');

    // Store the hashed PIN in admin settings
    await admin.firestore().collection('adminSettings').doc('security').set({
      adminPin: hashedPin,
      pinSetAt: admin.firestore.Timestamp.now(),
      pinSetBy: context.auth.uid
    }, { merge: true });

    console.log(`Admin PIN set by user: ${context.auth.uid}`);

    return {
      success: true,
      message: 'Admin PIN set successfully'
    };

  } catch (error) {
    console.error('Error setting admin PIN:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to set admin PIN',
      error
    );
  }
});

// Function to verify admin PIN
export const verifyAdminPin = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      // Verify the user is authenticated and is an admin
      if (!context.auth) {
        throw new functions.https.HttpsError(
          'unauthenticated',
          'User must be authenticated'
        );
      }

      const { pin } = data;

      if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'PIN must be exactly 8 digits'
        );
      }

      // Verify user is admin
      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError(
          'permission-denied',
          'Only admin users can verify PIN'
        );
      }

      // Get stored PIN hash
      const securityDoc = await admin.firestore().collection('adminSettings').doc('security').get();
      if (!securityDoc.exists || !securityDoc.data()?.adminPin) {
        throw new functions.https.HttpsError(
          'not-found',
          'Admin PIN not set. Please set up your PIN first.'
        );
      }

      // Hash the provided PIN and compare
      const hashedPin = createHash('sha256').update(pin).digest('hex');
      const storedPin = securityDoc.data()?.adminPin;

      if (hashedPin !== storedPin) {
        throw new functions.https.HttpsError(
          'permission-denied',
          'wrong pin unauthorised entry'
        );
      }

      // Update last access time
      await admin.firestore().collection('users').doc(context.auth.uid).update({
        lastAdminAccess: admin.firestore.Timestamp.now()
      });

      console.log(`Admin PIN verified for user: ${context.auth.uid}`);

      return {
        success: true,
        message: 'PIN verified successfully'
      };

    } catch (error) {
      console.error('Error verifying admin PIN:', error);
      throw error;
    }
  });

// Helper function to get wallet configuration
async function getWalletConfig() {
  const settingsDoc = await admin.firestore().collection('adminSettings').doc('wallet').get();

  if (settingsDoc.exists) {
    const data = settingsDoc.data();
    return {
      signupBonus: data?.signupBonus || 0,
      referralBonus: data?.referralBonus || 0,
      enableSignupBonus: data?.enableSignupBonus || false,
      enableReferralBonus: data?.enableReferralBonus || false
    };
  }

  return {
    signupBonus: 0,
    referralBonus: 0,
    enableSignupBonus: false,
    enableReferralBonus: false
  };
}

// Get wallet settings (admin only)
export const getWalletSettings = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Verify authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
      }

      // Verify admin role
      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
      }

      const settings = await getWalletConfig();

      return {
        success: true,
        settings
      };
    } catch (error) {
      console.error('Error in getWalletSettings:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Configure wallet settings (admin only)
export const configureWalletSettings = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Verify authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
      }

      // Verify admin role
      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
      }

      const { signupBonus, referralBonus, enableSignupBonus, enableReferralBonus } = data;

      // Validate input
      if (typeof signupBonus !== 'number' || signupBonus < 0 || signupBonus > 100) {
        throw new functions.https.HttpsError('invalid-argument', 'Signup bonus must be between 0 and 100');
      }

      if (typeof referralBonus !== 'number' || referralBonus < 0 || referralBonus > 100) {
        throw new functions.https.HttpsError('invalid-argument', 'Referral bonus must be between 0 and 100');
      }

      // Update settings
      await admin.firestore().collection('adminSettings').doc('wallet').set({
        signupBonus,
        referralBonus,
        enableSignupBonus: Boolean(enableSignupBonus),
        enableReferralBonus: Boolean(enableReferralBonus),
        updatedAt: admin.firestore.Timestamp.now(),
        updatedBy: context.auth.uid
      }, { merge: true });

      console.log(`Wallet settings updated by admin: ${context.auth.uid}`);

      return {
        success: true,
        message: 'Wallet settings updated successfully'
      };
    } catch (error) {
      console.error('Error configuring wallet settings:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Mark delivery as completed (for sellers)
export const markDeliveryCompleted = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
    }

    const { orderId } = data;

    if (!orderId) {
      throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
    }

    // Get order
    const orderRef = admin.firestore().collection('orders').doc(orderId);
    const orderDoc = await orderRef.get();

    if (!orderDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Order not found');
    }

    const orderData = orderDoc.data();

    // Verify seller
    if (orderData?.sellerId !== context.auth.uid) {
      throw new functions.https.HttpsError('permission-denied', 'Only the seller can mark delivery as completed');
    }

    // Verify order status
    if (orderData?.status !== 'payment_succeeded') {
      throw new functions.https.HttpsError('failed-precondition', 'Order must be in payment_succeeded status');
    }

    // Update order status to delivered and start 3-day countdown
    const deliveryDate = admin.firestore.Timestamp.now();
    const autoReleaseDate = admin.firestore.Timestamp.fromDate(
      new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now
    );

    await orderRef.update({
      status: 'delivered',
      deliveredAt: deliveryDate,
      autoReleaseDate: autoReleaseDate,
      deliveryMethod: 'in_person', // Seller manually marked as delivered
      updatedAt: admin.firestore.Timestamp.now()
    });

    // Send notification to buyer
    try {
      await admin.firestore().collection('notifications').add({
        userId: orderData.buyerId,
        type: 'delivery_confirmed',
        title: '📦 Item Delivered!',
        message: `The seller has marked your order "${orderData.title || orderData.listingTitle}" as delivered. Please enter your secret code to release payment.`,
        data: {
          orderId,
          secretCode: orderData.secretCode,
          autoReleaseDate: autoReleaseDate.toDate().toISOString(),
          showCodeEntry: true
        },
        priority: 'high',
        read: false,
        createdAt: admin.firestore.Timestamp.now()
      });
      console.log(`📬 Delivery notification sent to buyer: ${orderData.buyerId}`);
    } catch (notificationError) {
      console.error('Error sending delivery notification:', notificationError);
      // Don't fail the delivery marking if notification fails
    }

    console.log(`✅ Order ${orderId} marked as delivered by seller`);

    return {
      success: true,
      message: 'Delivery marked as completed. Buyer has 3 days to confirm receipt.',
      autoReleaseDate: autoReleaseDate.toDate().toISOString()
    };

  } catch (error) {
    console.error('Error marking delivery as completed:', error);
    throw error;
  }
});

// Automatic Shippo tracking analysis (scheduled function)
export const analyzeShippoTracking = functions.pubsub.schedule('every 2 hours').onRun(async () => {
  try {
    console.log('🔍 Starting Shippo tracking analysis...');

    // Get all orders with tracking that haven't been delivered yet
    const ordersQuery = await admin.firestore()
      .collection('orders')
      .where('status', '==', 'payment_succeeded')
      .where('trackingInfo.trackingNumber', '!=', null)
      .get();

    const promises: Promise<void>[] = [];

    ordersQuery.docs.forEach((doc) => {
      const orderData = doc.data();
      const orderId = doc.id;

      if (orderData.trackingInfo?.trackingNumber) {
        promises.push(checkShippoDeliveryStatus(orderId, orderData));
      }
    });

    await Promise.all(promises);
    console.log(`✅ Analyzed ${ordersQuery.docs.length} orders with tracking`);

  } catch (error) {
    console.error('❌ Error in Shippo tracking analysis:', error);
  }
});

// Helper function to check individual order delivery status
async function checkShippoDeliveryStatus(orderId: string, orderData: any): Promise<void> {
  try {
    const trackingNumber = orderData.trackingInfo.trackingNumber;

    // Mock Shippo API call (replace with actual Shippo API)
    // const shippoResponse = await fetch(`https://api.goshippo.com/tracks/${trackingNumber}`, {
    //   headers: { 'Authorization': `ShippoToken ${SHIPPO_API_KEY}` }
    // });
    // const trackingData = await shippoResponse.json();

    // For now, simulate delivery detection
    // In production, check: trackingData.tracking_status.status === 'DELIVERED'
    const isDelivered = false; // Replace with actual Shippo check

    if (isDelivered) {
      console.log(`📦 Shippo detected delivery for order: ${orderId}`);

      // Update order status to delivered and start 3-day countdown
      const deliveryDate = admin.firestore.Timestamp.now();
      const autoReleaseDate = admin.firestore.Timestamp.fromDate(
        new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now
      );

      await admin.firestore().collection('orders').doc(orderId).update({
        status: 'delivered',
        deliveredAt: deliveryDate,
        autoReleaseDate: autoReleaseDate,
        deliveryMethod: 'mail', // Detected via Shippo tracking
        updatedAt: admin.firestore.Timestamp.now()
      });

      // Send notification to buyer
      await admin.firestore().collection('notifications').add({
        userId: orderData.buyerId,
        type: 'delivery_detected',
        title: '📦 Package Delivered!',
        message: `Your order "${orderData.title || orderData.listingTitle}" has been delivered according to tracking. Please enter your secret code to release payment.`,
        data: {
          orderId,
          secretCode: orderData.secretCode,
          autoReleaseDate: autoReleaseDate.toDate().toISOString(),
          showCodeEntry: true,
          trackingNumber
        },
        priority: 'high',
        read: false,
        createdAt: admin.firestore.Timestamp.now()
      });

      console.log(`📬 Auto-delivery notification sent for order: ${orderId}`);
    }

  } catch (error) {
    console.error(`❌ Error checking delivery for order ${orderId}:`, error);
  }
}

// Auto-release funds after 3 days (scheduled function)
export const autoReleaseFunds = functions.pubsub.schedule('every 1 hours').onRun(async () => {
  try {
    console.log('⏰ Starting auto-release check...');

    const now = admin.firestore.Timestamp.now();

    // Find orders eligible for auto-release (delivered > 3 days ago)
    const ordersQuery = await admin.firestore()
      .collection('orders')
      .where('status', '==', 'delivered')
      .where('autoReleaseDate', '<=', now)
      .get();

    const promises: Promise<void>[] = [];

    ordersQuery.docs.forEach((doc) => {
      const orderData = doc.data();
      const orderId = doc.id;

      // Only auto-release if funds haven't been released yet
      if (!orderData.fundsReleased && !orderData.completedAt) {
        promises.push(autoReleaseOrderFunds(orderId, orderData));
      }
    });

    await Promise.all(promises);
    console.log(`✅ Auto-released ${promises.length} orders`);

  } catch (error) {
    console.error('❌ Error in auto-release:', error);
  }
});

// Helper function to auto-release funds for a specific order
async function autoReleaseOrderFunds(orderId: string, orderData: any): Promise<void> {
  try {
    console.log(`💰 Auto-releasing funds for order: ${orderId}`);

    // Update order status to completed
    await admin.firestore().collection('orders').doc(orderId).update({
      status: 'completed',
      fundsReleased: true,
      fundsReleasedAt: admin.firestore.Timestamp.now(),
      completedAt: admin.firestore.Timestamp.now(),
      autoReleased: true, // Flag to indicate this was auto-released
      updatedAt: admin.firestore.Timestamp.now()
    });

    // Update listing to sold (if not already sold)
    if (orderData?.listingId) {
      const listingRef = admin.firestore().collection('listings').doc(orderData.listingId);
      const listingDoc = await listingRef.get();

      if (listingDoc.exists && listingDoc.data()?.status !== 'sold') {
        await listingRef.update({
          status: 'sold',
          soldAt: admin.firestore.Timestamp.now(),
          updatedAt: admin.firestore.Timestamp.now()
        });
        console.log(`✅ Listing ${orderData.listingId} marked as sold (auto-release)`);
      }
    }

    // Send notifications to both buyer and seller
    const notifications = [];

    // Notify buyer
    notifications.push(
      admin.firestore().collection('notifications').add({
        userId: orderData.buyerId,
        type: 'funds_auto_released',
        title: '⏰ Funds Auto-Released',
        message: `Funds for your order "${orderData.title || orderData.listingTitle}" have been automatically released to the seller after 3 days.`,
        data: { orderId },
        priority: 'medium',
        read: false,
        createdAt: admin.firestore.Timestamp.now()
      })
    );

    // Notify seller
    notifications.push(
      admin.firestore().collection('notifications').add({
        userId: orderData.sellerId,
        type: 'funds_received',
        title: '💰 Payment Received',
        message: `You have received payment for "${orderData.title || orderData.listingTitle}" (auto-released after 3 days).`,
        data: { orderId },
        priority: 'high',
        read: false,
        createdAt: admin.firestore.Timestamp.now()
      })
    );

    await Promise.all(notifications);
    console.log(`📬 Auto-release notifications sent for order: ${orderId}`);

  } catch (error) {
    console.error(`❌ Error auto-releasing order ${orderId}:`, error);
  }
}

// Fix admin user function (for setup)
export const fixAdminUser = functions.https.onCall(async (data, _context) => {
  try {
    const { email } = data;

    if (!email) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Email is required'
      );
    }

    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(email);

    // Set custom claims with both admin and role
    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: true,
      role: 'admin'
    });

    // Update or create user profile in Firestore with complete admin setup
    await admin.firestore().collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      name: userRecord.displayName || 'Admin User',
      email: userRecord.email,
      role: 'admin',
      university: 'Hive Campus Admin',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: true,
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    }, { merge: true });

    console.log(`Admin user fixed for: ${email}`);

    return {
      success: true,
      message: `Admin user fixed for ${email}`,
      uid: userRecord.uid
    };

  } catch (error) {
    console.error('Error fixing admin user:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to fix admin user',
      error
    );
  }
});

// Helper function to verify authentication
function verifyAuth(context: functions.https.CallableContext) {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
  }
  return context.auth;
}

// Get listings with filtering
export const getListings = functions.https.onCall(async (data, context) => {
  try {
    verifyAuth(context);

    const {
      university,
      category,
      type,
      condition,
      minPrice,
      maxPrice,
      ownerId,
      status = 'active',
      limit = 20,
      lastVisible
    } = data;

    // Get current user's university for visibility filtering
    const userDoc = await admin.firestore().collection('users').doc(context.auth!.uid).get();
    const currentUserUniversity = userDoc.data()?.university;

    let query = admin.firestore().collection('listings')
      .where('status', '==', status)
      .orderBy('createdAt', 'desc');

    // Apply filters if provided
    if (university) {
      query = query.where('university', '==', university);
    }

    if (category) {
      query = query.where('category', '==', category);
    }

    if (type) {
      query = query.where('type', '==', type);
    }

    if (condition) {
      query = query.where('condition', '==', condition);
    }

    if (ownerId) {
      query = query.where('ownerId', '==', ownerId);
    }

    // Apply limit
    query = query.limit(limit);

    // Apply pagination if lastVisible is provided
    if (lastVisible) {
      const lastDoc = await admin.firestore().collection('listings').doc(lastVisible).get();
      if (lastDoc.exists) {
        query = query.startAfter(lastDoc);
      }
    }

    const snapshot = await query.get();
    const listings: any[] = [];
    let lastVisibleId: string | null = null;

    snapshot.forEach(doc => {
      const listing = doc.data();

      // Apply visibility filtering
      const isVisible = listing.visibility === 'public' ||
                       (listing.visibility === 'university' && listing.university === currentUserUniversity);

      if (!isVisible) {
        return; // Skip this listing
      }

      // Apply price filtering in memory (can't do range queries along with other filters in Firestore)
      const price = listing.price;
      if ((minPrice === undefined || price >= minPrice) &&
          (maxPrice === undefined || price <= maxPrice)) {
        listings.push({
          id: doc.id,
          ...listing
        });
      }

      // Set the last visible document ID for pagination
      lastVisibleId = doc.id;
    });

    return {
      success: true,
      data: {
        listings,
        lastVisible: lastVisibleId,
        total: listings.length
      }
    };
  } catch (error) {
    console.error('Error getting listings:', error);
    throw new functions.https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error');
  }
});

// Get a single listing by ID
export const getListingById = functions.https.onCall(async (data, context) => {
  try {
    verifyAuth(context);

    const { listingId } = data;

    if (!listingId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Listing ID is required'
      );
    }

    const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();

    if (!listingDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'Listing not found'
      );
    }

    const listing = listingDoc.data();

    // Don't return deleted listings unless it's the owner
    if (listing?.status === 'deleted' && listing.ownerId !== context.auth?.uid) {
      throw new functions.https.HttpsError(
        'not-found',
        'Listing not found'
      );
    }

    return {
      success: true,
      data: {
        id: listingDoc.id,
        ...listing
      }
    };
  } catch (error) {
    console.error('Error getting listing by ID:', error);
    throw error;
  }
});

// Get wallet balance and history
export const getWalletData = functions.https.onCall(async (_data, context) => {
  try {
    verifyAuth(context);

    const userId = context.auth!.uid;
    const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();

    if (!walletDoc.exists) {
      // Initialize wallet if it doesn't exist
      const walletData = {
        userId,
        balance: 0,
        referralCode: `user${userId.substring(0, 6)}`,
        usedReferral: false,
        history: [],
        grantedBy: 'system',
        createdAt: admin.firestore.Timestamp.now(),
        lastUpdated: admin.firestore.Timestamp.now()
      };

      await admin.firestore().collection('wallets').doc(userId).set(walletData);
      return walletData;
    }

    return walletDoc.data();
  } catch (error) {
    console.error('Error getting wallet data:', error);
    throw new functions.https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error');
  }
});

// Get Stripe Connect account status
export const getStripeConnectAccountStatus = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      console.log('Fetching Stripe Connect account status for user:', userId);

      // Get connect account from Firestore
      const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();

      if (!connectAccountDoc.exists) {
        console.log(`No connect account found for user: ${userId}`);
        return null;
      }

      const connectAccount = connectAccountDoc.data();
      const stripeAccountId = connectAccount?.stripeAccountId;

      if (!stripeAccountId) {
        console.log(`No Stripe account ID found for user: ${userId}`);
        return null;
      }

      // Fetch real-time data from Stripe
      let stripeAccount;
      try {
        stripeAccount = await stripe.accounts.retrieve(stripeAccountId);
        console.log('Fetched Stripe account data:', stripeAccount.id);
      } catch (stripeError) {
        console.error('Error fetching Stripe account:', stripeError);
        // Return cached data if Stripe API fails
        return {
          accountId: stripeAccountId,
          onboardingUrl: connectAccount?.onboardingUrl || null,
          dashboardUrl: connectAccount?.dashboardUrl || null,
          isOnboarded: connectAccount?.isOnboarded || false,
          chargesEnabled: connectAccount?.chargesEnabled || false,
          payoutsEnabled: connectAccount?.payoutsEnabled || false,
        };
      }

      // Update Firestore with latest Stripe data
      const isOnboarded = stripeAccount.details_submitted && stripeAccount.charges_enabled;
      const updateData: any = {
        isOnboarded,
        chargesEnabled: stripeAccount.charges_enabled || false,
        payoutsEnabled: stripeAccount.payouts_enabled || false,
        detailsSubmitted: stripeAccount.details_submitted || false,
        updatedAt: admin.firestore.Timestamp.now(),
      };

      // Add dashboard URL if account is onboarded
      if (isOnboarded && !connectAccount?.dashboardUrl) {
        try {
          const loginLink = await stripe.accounts.createLoginLink(stripeAccountId);
          updateData.dashboardUrl = loginLink.url;
        } catch (loginError) {
          console.error('Error creating login link:', loginError);
        }
      }

      await admin.firestore().collection('connectAccounts').doc(userId).update(updateData);

      return {
        accountId: stripeAccountId,
        onboardingUrl: connectAccount?.onboardingUrl || null,
        dashboardUrl: updateData.dashboardUrl || connectAccount?.dashboardUrl || null,
        isOnboarded,
        chargesEnabled: stripeAccount.charges_enabled || false,
        payoutsEnabled: stripeAccount.payouts_enabled || false,
      };
    } catch (error) {
      console.error('Error in getStripeConnectAccountStatus:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get pending payouts for seller
export const getSellerPendingPayouts = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      console.log('Fetching pending payouts for user:', userId);

      let pendingPayouts: any[] = [];

      try {
        // Get orders where this user is the seller and payment has succeeded but funds not released
        const ordersQuery = await admin.firestore()
          .collection('orders')
          .where('sellerId', '==', userId)
          .where('status', 'in', ['payment_succeeded', 'delivered'])
          .where('fundsReleased', '==', false)
          .get();

        console.log(`Found ${ordersQuery.docs.length} pending orders for seller ${userId}`);

        pendingPayouts = ordersQuery.docs.map(doc => {
          const orderData = doc.data();
          const totalAmount = orderData.totalAmount || 0;

          // Calculate commission (8% for textbooks, 10% for others, $0.50 flat fee for items $1-$5)
          let commissionAmount = 0;
          if (totalAmount <= 5) {
            commissionAmount = 0.50;
          } else {
            const commissionRate = orderData.category === 'textbooks' ? 0.08 : 0.10;
            commissionAmount = totalAmount * commissionRate;
          }

          const sellerAmount = Math.max(0, totalAmount - commissionAmount);

          return {
            orderId: doc.id,
            amount: totalAmount,
            commissionAmount: commissionAmount,
            sellerAmount: sellerAmount,
            paymentIntentId: orderData.paymentIntentId || null,
            createdAt: orderData.createdAt || orderData.paymentCompletedAt,
            status: orderData.status,
            listingTitle: orderData.listingTitle || orderData.title,
            buyerName: orderData.buyerName || 'Anonymous'
          };
        });

        console.log(`Processed ${pendingPayouts.length} pending payouts`);

      } catch (firestoreError) {
        console.error('Error querying Firestore for pending payouts:', firestoreError);
        // Return empty array instead of throwing error
        pendingPayouts = [];
      }

      return pendingPayouts;

    } catch (error) {
      console.error('Error in getSellerPendingPayouts:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Create Stripe Connect account
export const createStripeConnectAccount = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const { accountType } = data;
      const userId = context.auth.uid;

      if (!accountType || !['student', 'merchant'].includes(accountType)) {
        throw new functions.https.HttpsError('invalid-argument', 'Valid accountType is required');
      }

      console.log(`Creating Stripe Connect account for user ${userId}, type: ${accountType}`);

      // Check if user already has a connect account
      const existingAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();

      if (existingAccountDoc.exists) {
        const existingAccount = existingAccountDoc.data();
        console.log('User already has a connect account:', existingAccount?.stripeAccountId);

        return {
          accountId: existingAccount?.stripeAccountId,
          onboardingUrl: existingAccount?.onboardingUrl || null,
          message: 'Account already exists'
        };
      }

      // Get user data
      const userDoc = await admin.firestore().collection('users').doc(userId).get();
      if (!userDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'User profile not found');
      }

      const userData = userDoc.data();

      // Create a real Stripe Connect Express account
      const account = await stripe.accounts.create({
        type: 'express',
        country: 'US',
        email: userData?.email,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        business_type: accountType === 'merchant' ? 'company' : 'individual',
        metadata: {
          userId,
          accountType,
          platform: 'hive_campus'
        }
      });

      console.log(`Created Stripe Connect account ${account.id} for user ${userId}`);

      // Create account link for onboarding
      const accountLink = await stripe.accountLinks.create({
        account: account.id,
        refresh_url: `${process.env.APP_URL || 'https://h1c1-798a8.web.app'}/profile?stripe_refresh=true`,
        return_url: `${process.env.APP_URL || 'https://h1c1-798a8.web.app'}/profile?stripe_success=true`,
        type: 'account_onboarding',
      });

      // Store the Connect account in Firestore
      await admin.firestore().collection('connectAccounts').doc(userId).set({
        userId: userId,
        stripeAccountId: account.id,
        accountType: accountType,
        isOnboarded: false,
        chargesEnabled: account.charges_enabled || false,
        payoutsEnabled: account.payouts_enabled || false,
        detailsSubmitted: account.details_submitted || false,
        onboardingUrl: accountLink.url,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      });

      console.log(`Stored Connect account ${account.id} in Firestore for user ${userId}`);

      return {
        accountId: account.id,
        onboardingUrl: accountLink.url,
        message: 'Connect account created successfully'
      };

    } catch (error) {
      console.error('Error in createStripeConnectAccount:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get Stripe Connect onboarding link
export const getStripeConnectOnboardingLink = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      const { refreshUrl, returnUrl } = data;

      console.log(`Getting onboarding link for user ${userId}`);

      // Get connect account from Firestore
      const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();

      if (!connectAccountDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'No Stripe Connect account found. Please create an account first.');
      }

      const connectAccount = connectAccountDoc.data();
      const stripeAccountId = connectAccount?.stripeAccountId;

      if (!stripeAccountId) {
        throw new functions.https.HttpsError('not-found', 'No Stripe account ID found.');
      }

      // Check if already onboarded by fetching from Stripe
      const stripeAccount = await stripe.accounts.retrieve(stripeAccountId);
      const isOnboarded = stripeAccount.details_submitted && stripeAccount.charges_enabled;

      if (isOnboarded) {
        // Create dashboard login link
        const loginLink = await stripe.accounts.createLoginLink(stripeAccountId);
        return {
          onboardingUrl: loginLink.url,
          isOnboarded: true
        };
      }

      // Create fresh onboarding link
      const baseUrl = process.env.APP_URL || 'https://h1c1-798a8.web.app';
      const accountLink = await stripe.accountLinks.create({
        account: stripeAccountId,
        refresh_url: refreshUrl || `${baseUrl}/profile?stripe_refresh=true`,
        return_url: returnUrl || `${baseUrl}/profile?stripe_success=true`,
        type: 'account_onboarding',
      });

      // Update Firestore with new onboarding URL
      await admin.firestore().collection('connectAccounts').doc(userId).update({
        onboardingUrl: accountLink.url,
        updatedAt: admin.firestore.Timestamp.now(),
      });

      return {
        onboardingUrl: accountLink.url,
        isOnboarded: false
      };

    } catch (error) {
      console.error('Error in getStripeConnectOnboardingLink:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });
